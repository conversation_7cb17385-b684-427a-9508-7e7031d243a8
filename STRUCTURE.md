# Project Structure

This document outlines the organized structure of the Product Hunt Dashboard project after reorganization for better maintainability.

## 📁 Root Directory
Core web interface files:

- **`index.php`** - Main dashboard interface for viewing and managing Product Hunt data
- **`login.php`** - User authentication page with secure login functionality
- **`logout.php`** - Secure logout handler that clears sessions and redirects
- **`README.md`** - Project documentation and usage instructions
- **`STRUCTURE.md`** - This file - detailed project structure documentation

## 📁 config/
Configuration and database setup files:

- **`config.php`** - Central configuration file containing database credentials and app settings
- **`database_setup.php`** - Database schema creation and initialization script

## 📁 api/
API endpoints for web interface:

- **`manual_url_api.php`** - REST API for manual URL override functionality

## 📁 includes/
Shared utility and authentication files:

- **`auth.php`** - Authentication system with session management and security features
- **`url_utils.php`** - URL processing utilities (cleaning, redirect resolution)

## 📁 scripts/
Command-line scripts for data processing and maintenance:

- **`fetch_products.php`** - Fetches products from Product Hunt API with date filtering
- **`resolve_urls.php`** - Resolves external URLs with Playwright fallback for failed URLs

### 📁 scripts/analysis/
Scripts for data analysis and reporting:

- **`check_data.php`** - Analyze database statistics and data quality
- **`check_taglines.php`** - Analyze tagline coverage and patterns

### 📁 scripts/maintenance/
Scripts for database maintenance and cleanup:

- **`fix_missing_product_urls.php`** - Generate missing Product Hunt URLs
- **`clean_existing_urls.php`** - Clean existing URLs in database
- **`clean_product_urls.php`** - Clean Product Hunt URLs specifically

## 📁 assets/
Static assets for the web interface:

### 📁 assets/img/
Images and graphics used in the dashboard interface

## 🗑️ Removed Files
The following temporary/duplicate files were removed during organization:

- `update_existing_urls.php` - Duplicate functionality
- `update_urls.php` - Temporary script
- `view_products.php` - Replaced by index.php

## 🚀 Usage After Reorganization

### Main Operations
```bash
# Fetch today's products (from root directory)
php fetch_today_products.php --date=2025-08-30

# View web interface
# Navigate to: http://localhost:8000/
```

### Analysis Scripts
```bash
# Check data quality
php scripts/analysis/check_data.php

# Analyze taglines
php scripts/analysis/check_taglines.php
```

### Maintenance Scripts
```bash
# Fix missing Product Hunt URLs
php scripts/maintenance/fix_missing_product_urls.php

# Clean existing URLs
php scripts/maintenance/clean_existing_urls.php
```

## 📋 File Dependencies
All moved scripts have been updated with correct relative paths:
- Scripts in `scripts/*/` use `../../config.php`
- Core files (index.php, fetch_today_products.php) use `config.php`

## 🎯 Benefits of This Structure
1. **Clear separation** of concerns (core, analysis, maintenance)
2. **Easier maintenance** - related scripts are grouped together
3. **Reduced clutter** - removed duplicate/temporary files
4. **Better navigation** - logical folder hierarchy
5. **Scalability** - easy to add new scripts in appropriate folders
