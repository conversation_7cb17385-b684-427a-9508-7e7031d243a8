<?php
/**
 * Product Hunt Products Display
 * Web interface for viewing and filtering products
 */

require_once 'config/config.php';
require_once 'includes/auth.php';

// Require authentication
Auth::requireAuth();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Hunt Dashboard</title>
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="https://<?php echo $_SERVER['HTTP_HOST']; ?>/assets/img/favicon.png">

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 13px;
        }
        .container {
            max-width: none;
        }
        h1 {
            text-align: left;
            font-size: 24px;
        }
        .stats {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .stats-left {
            display: flex;
            align-items: center;
        }

        /* Make table horizontally scrollable */
        .dataTables_wrapper {
            overflow-x: auto;
            width: 100%;
        }

        .dataTables_scroll {
            overflow-x: auto;
        }

        .dataTables_scrollBody {
            overflow-x: auto !important;
        }

        table.dataTable {
            width: 100% !important;
            white-space: nowrap;
            font-size: 13px;
            table-layout: fixed !important;
        }

        table.dataTable td,
        table.dataTable th {
            vertical-align: top !important;
        }

        /* Force fixed column widths */
        table.dataTable th:nth-child(1), table.dataTable td:nth-child(1) { width: 150px !important; max-width: 150px !important; font-weight: bold !important; }
        table.dataTable th:nth-child(2), table.dataTable td:nth-child(2) { width: 120px !important; max-width: 120px !important; }

        /* Manual URL popup styles */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
        }

        .popup-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            width: 450px;
            max-width: 90%;
            font-family: Arial, sans-serif;
            font-size: 13px;
        }

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            margin: 0;
        }

        .popup-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }

        .popup-close {
            cursor: pointer;
            font-size: 20px;
            color: #333;
            line-height: 1;
            padding: 5px 10px;
            background: #f0f0f0;
            border: 1px solid #ccc;
        }

        .popup-close:hover {
            color: #333;
            background: #e0e0e0;
        }

        .popup-form {
            padding: 20px;
        }

        .popup-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: normal;
        }

        .popup-form input[type="url"] {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
            font-size: 13px;
            box-sizing: border-box;
        }

        .popup-buttons {
            text-align: right;
            margin: 0;
        }

        .popup-buttons button {
            margin-left: 8px;
            padding: 8px 16px;
            border: 1px solid #ccc;
            background: #f0f0f0;
            cursor: pointer;
            font-size: 13px;
        }

        .popup-buttons button:hover {
            background: #e0e0e0;
        }

        .manual-url-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px;
            margin: 5px 0;
            font-size: 12px;
        }

        /* Temporary notice styles */
        .temp-notice {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #f8f9fa;
            border: 1px solid #ccc;
            padding: 12px 16px;
            font-size: 13px;
            font-family: Arial, sans-serif;
            z-index: 1001;
            max-width: 300px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .temp-notice.show {
            opacity: 1;
            transform: translateY(0);
        }

        .temp-notice.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .temp-notice.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        table.dataTable th:nth-child(3), table.dataTable td:nth-child(3) { width: 200px !important; max-width: 200px !important; }
        table.dataTable th:nth-child(4), table.dataTable td:nth-child(4) { width: 40px !important; max-width: 40px !important; }
        table.dataTable th:nth-child(5), table.dataTable td:nth-child(5) { width: 40px !important; max-width: 40px !important; }
        table.dataTable th:nth-child(6), table.dataTable td:nth-child(6) { width: 60px !important; max-width: 60px !important; }
        table.dataTable th:nth-child(7), table.dataTable td:nth-child(7) { width: 120px !important; max-width: 120px !important; }
        table.dataTable th:nth-child(8), table.dataTable td:nth-child(8) { width: 200px !important; max-width: 200px !important; }
        table.dataTable th:nth-child(9), table.dataTable td:nth-child(9) { width: 80px !important; max-width: 80px !important; }
        table.dataTable th:nth-child(10), table.dataTable td:nth-child(10) { width: 80px !important; max-width: 80px !important; }

        /* Text truncation for long content */
        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .text-center {
            text-align: center;
        }

        /* URL link styling to prevent overflow */
        .url-link {
            color: #0066cc;
            word-break: break-all;
            overflow-wrap: break-word;
            hyphens: auto;
            max-width: 100%;
        }

        /* Specific styling for Links column */
        table.dataTable th:nth-child(8),
        table.dataTable td:nth-child(8) {
            word-break: break-all;
            overflow-wrap: break-word;
            white-space: normal !important;
        }

        table.dataTable td:nth-child(8) a {
            color: #0066cc;
        }

        /* Limit description to 3 lines with ellipsis */
        .product-description {
            white-space: normal;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            min-width: 200px;
        }

        /* Keep other elements normal */
        .categories-list,
        .product-tagline {
            white-space: normal;
            word-wrap: break-word;
            min-width: 200px;
        }

        .product-tagline {
            min-width: 150px;
            max-width: 200px;
        }

        .filter-container a {
            text-decoration: none;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h1 style="margin: 0;"><a href="https://<?php echo $_SERVER['HTTP_HOST']; ?>" style="text-decoration: none; color: inherit;">Product Hunt Dashboard</a></h1>
            <div style="display: flex; align-items: center; gap: 15px;">
                <span>Welcome, <?= htmlspecialchars(Auth::getUsername(), ENT_QUOTES, 'UTF-8') ?></span>
                <a href="logout.php?token=<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>" style="padding: 4px 8px; text-decoration: none; border: 1px solid #ccc; color: #333; background: #f0f0f0; font-size: 13px;">
                   Logout
                </a>
            </div>
        </div>
        
        <?php
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            
            // Get statistics
            $statsStmt = $pdo->query("
                SELECT
                    COUNT(*) as total_products,
                    COUNT(DISTINCT date_created) as unique_dates,
                    MIN(date_created) as oldest_fetch_date,
                    MAX(date_created) as newest_fetch_date
                FROM products
            ");
            $stats = $statsStmt->fetch();

            // Get filter counts
            $filterCountsStmt = $pdo->query("
                SELECT
                    SUM(CASE WHEN p.external_url IS NULL OR p.external_url = '' THEN 1 ELSE 0 END) as empty_count,
                    SUM(CASE WHEN (url_override.rule IS NOT NULL OR status_override.rule IS NOT NULL) THEN 1 ELSE 0 END) as manual_count,
                    SUM(CASE WHEN (COALESCE(status_override.rule, p.external_url_status) = 'down') THEN 1 ELSE 0 END) as down_count,
                    SUM(CASE WHEN p.external_url_status IS NULL THEN 1 ELSE 0 END) as unprocessed_count
                FROM products p
                LEFT JOIN overrides url_override ON p.id = url_override.product_id AND url_override.type = 'url'
                LEFT JOIN overrides status_override ON p.id = status_override.product_id AND status_override.type = 'status'
            ");
            $filterCounts = $filterCountsStmt->fetch();

            // Get available dates for the date picker
            $datesStmt = $pdo->prepare("SELECT DISTINCT DATE(date_created) as date FROM products ORDER BY date DESC");
            $datesStmt->execute();
            $availableDates = $datesStmt->fetchAll(PDO::FETCH_COLUMN);

            // Get all products with their categories and overrides
            $sql = "
                SELECT
                    p.*,
                    GROUP_CONCAT(c.name ORDER BY c.name SEPARATOR ', ') as categories,
                    url_override.rule as manual_url,
                    url_override.created_at as manual_url_created_at,
                    status_override.rule as status_override
                FROM products p
                LEFT JOIN product_categories pc ON p.id = pc.product_id
                LEFT JOIN categories c ON pc.category_id = c.id
                LEFT JOIN overrides url_override ON p.id = url_override.product_id AND url_override.type = 'url'
                LEFT JOIN overrides status_override ON p.id = status_override.product_id AND status_override.type = 'status'
                GROUP BY p.id
                ORDER BY p.votes_count DESC
            ";
            $stmt = $pdo->query($sql);
            $products = $stmt->fetchAll();
            
        } catch (PDOException $e) {
            echo "<div style='color: red; text-align: center;'>Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
            exit;
        }
        ?>
        
        <!-- Statistics -->
        <div class="stats">
            <div class="stats-left">
                <span>Total Products: <?= number_format($stats['total_products']) ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Launch Days: <?= $stats['unique_dates'] ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Newest Fetch: <?= $stats['newest_fetch_date'] ? date('M j, Y', strtotime($stats['newest_fetch_date'])) : '-' ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Oldest Fetch: <?= $stats['oldest_fetch_date'] ? date('M j, Y', strtotime($stats['oldest_fetch_date'])) : '-' ?></span>
            </div>
            <div class="live-time">
                <span id="localTime">Loading...</span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span id="pacificTime">Loading...</span>
            </div>
        </div>

        <!-- Status Filters -->
        <div class="filter-container" style="margin: 0 0 15px;">
            <a href="javascript:void(0);" id="filterAll" class="filter-link active" style="margin: 0 5px 0 0; font-weight: bold;">All Products (<?= number_format($stats['total_products']) ?>)</a>
            <a href="javascript:void(0);" id="filterEmpty" class="filter-link" style="margin: 0 5px;">Empty URL (<?= number_format($filterCounts['empty_count']) ?>)</a>
            <a href="javascript:void(0);" id="filterManual" class="filter-link" style="margin: 0 5px;">Manual URL (<?= number_format($filterCounts['manual_count']) ?>)</a>
            <a href="javascript:void(0);" id="filterDown" class="filter-link" style="margin: 0 5px;">Down (<?= number_format($filterCounts['down_count']) ?>)</a>
            <a href="javascript:void(0);" id="filterUnprocessed" class="filter-link" style="margin: 0 5px;">Unprocessed URL (<?= number_format($filterCounts['unprocessed_count']) ?>)</a>
            <span style="margin: 0 10px; color: #ccc;">|</span>
            <strong style="margin: 0 5px">Filter by date:</strong>
            <input type="date" id="datePicker" style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 3px; margin: 0 5px;">
            <a href="javascript:void(0);" id="resetDateFilter" style="margin: 0 5px; color: #333; text-decoration: none;">Reset</a>
        </div>

        <!-- Results Info and Controls -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <div id="resultsInfo" style="font-size: 14px;"></div>
            <div style="display: flex; align-items: center; gap: 15px;">
                <div style="display: flex; align-items: center; gap: 5px;">
                    <select id="productsPerPage" style="padding: 4px 8px;">
                        <option value="5">5</option>
                        <option value="7">7</option>
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="-1">All</option>
                    </select>
                    <span style="font-size: 14px;">products per page</span>
                </div>
                <div style="display: flex; align-items: center; gap: 5px;">
                    <input type="search" id="searchBox" placeholder="Search products..." style="padding: 4px 8px; width: 200px;">
                </div>
            </div>
        </div>

        <!-- Products DataTable -->
        <table id="productsTable" class="display" style="width:100%">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Tagline</th>
                    <th>Description</th>
                    <th>Votes</th>
                    <th>Rating</th>
                    <th>Launch Date</th>
                    <th>Categories</th>
                    <th>Website</th>
                    <th>Created</th>
                    <th>Updated</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($products as $product): ?>
                <tr>
                    <td>
                        <?php if ($product['product_url']): ?>
                            <a href="<?= htmlspecialchars($product['product_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['name']) ?></a>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div class="product-tagline">
                            <?= $product['tagline'] ? htmlspecialchars($product['tagline']) : '-' ?>
                        </div>
                    </td>
                    <td>
                        <div class="product-description">
                            <?= $product['description'] ? htmlspecialchars($product['description']) : '-' ?>
                        </div>
                    </td>
                    <td>
                        <?= number_format($product['votes_count']) ?>
                    </td>
                    <td>
                        <?php if ($product['review_rating']): ?>
                            <?= number_format($product['review_rating'], 1) ?>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <td>
                        <?= $product['date_created'] ? date('M j, Y', strtotime($product['date_created'])) : '-' ?>
                    </td>
                    <td>
                        <div class="categories-list">
                            <?= $product['categories'] ? htmlspecialchars($product['categories']) : '-' ?>
                        </div>
                    </td>
                    <td>
                        <?php
                        // Determine effective status (status override takes precedence)
                        $effectiveStatus = $product['status_override'] ?: $product['external_url_status'];
                        ?>
                        <?php if ($product['manual_url']): ?>
                            <!-- Manual URL override exists -->
                            <?php if ($effectiveStatus === 'down'): ?>
                                <a href="<?= htmlspecialchars($product['manual_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['manual_url']) ?></a>
                                <div style="margin-top: 10px;">
                                <span style="font-size: 11px;">🔧</span>
                                <span style="margin-left: 3px;">URL overridden</span>
                                <br>
                                <?php if ($product['status_override']): ?>
                                    <span style="font-size: 11px; margin-left: 3px;">📌</span>
                                    <span style="margin-left: 3px;">Status overridden to '<?= htmlspecialchars($product['status_override']) ?>'</span>
                                    <br>
                                    <a href="javascript:void(0);" onclick="removeStatusOverride('<?= htmlspecialchars($product['id']) ?>')">remove status override</a>
                                <?php else: ?>
                                    <span>⚠️</span>
                                    <span style="margin-left: 3px;">Down</span>
                                    <br>
                                    <a href="javascript:void(0);" onclick="markUrlStatus('<?= htmlspecialchars($product['id']) ?>', 'up')">mark as up</a>
                                <?php endif; ?>
                                <a href="javascript:void(0);" onclick="removeManualUrl('<?= htmlspecialchars($product['id']) ?>')" style="margin-left: 5px;">remove URL override</a>
                            <?php else: ?>
                                <a href="<?= htmlspecialchars($product['manual_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['manual_url']) ?></a>
                                <div style="margin-top: 10px;">
                                <span style="font-size: 11px;">🔧</span>
                                <span style="margin-left: 3px;">URL overridden</span>
                                <br>
                                <?php if ($product['status_override']): ?>
                                    <span style="font-size: 11px; margin-left: 3px;">📌</span>
                                    <span style="margin-left: 3px;">Status overridden to '<?= htmlspecialchars($product['status_override']) ?>'</span>
                                    <br>
                                    <a href="javascript:void(0);" onclick="removeStatusOverride('<?= htmlspecialchars($product['id']) ?>')">remove status override</a>
                                <?php else: ?>
                                    <!-- No status override yet, allow adding one -->
                                    <a href="javascript:void(0);" onclick="markUrlStatus('<?= htmlspecialchars($product['id']) ?>', 'up')">mark as up</a>
                                <?php endif; ?>
                                <a href="javascript:void(0);" onclick="removeManualUrl('<?= htmlspecialchars($product['id']) ?>')" style="margin-left: 5px; color: #666;">remove URL override</a>
                            <?php endif; ?>
                            <?php if ($product['external_url'] && $product['external_url_status'] === 'up' && !$product['status_override']): ?>
                                <div class="manual-url-notice">
                                    URL resolved automatically.
                                </div>
                            <?php endif; ?>
                        <?php elseif ($product['external_url'] && $effectiveStatus === 'up'): ?>
                            <a href="<?= htmlspecialchars($product['external_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['external_url']) ?></a>
                            <?php if ($product['status_override']): ?>
                                <div style="margin-top: 10px;">
                                <span style="font-size: 11px;">📌</span>
                                <span style="margin-left: 3px;">Status overridden to '<?= htmlspecialchars($product['status_override']) ?>'</span>
                                <br>
                                <a href="javascript:void(0);" onclick="removeStatusOverride('<?= htmlspecialchars($product['id']) ?>')">remove status override</a>
                            <?php endif; ?>
                        <?php elseif ($product['external_url'] && $effectiveStatus === 'down'): ?>
                            <a href="<?= htmlspecialchars($product['external_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['external_url']) ?></a>
                            <div style="margin-top: 10px;">
                            <?php if ($product['status_override']): ?>
                                <span style="font-size: 11px;">📌</span>
                                <span style="margin-left: 3px;">Status overridden to '<?= htmlspecialchars($product['status_override']) ?>'</span>
                                <br>
                                <a href="javascript:void(0);" onclick="removeStatusOverride('<?= htmlspecialchars($product['id']) ?>')">remove status override</a>
                            <?php else: ?>
                                <span style="font-size: 11px;">⚠️</span>
                                <span style="margin-left: 3px;">Down</span>
                                <br>
                                <a href="javascript:void(0);" onclick="markUrlStatus('<?= htmlspecialchars($product['id']) ?>', 'up')">mark as up</a>
                            <?php endif; ?>
                            <a href="javascript:void(0);" onclick="openManualUrlPopup('<?= htmlspecialchars($product['id']) ?>')" style="margin-left: 5px;">add manual URL</a>
                        <?php elseif ($effectiveStatus === 'up' && $product['status_override']): ?>
                            <!-- Status overridden to 'up' but no external_url -->
                            <?php if ($product['external_url']): ?>
                                <a href="<?= htmlspecialchars($product['external_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['external_url']) ?></a>
                            <?php else: ?>
                                <a href="<?= htmlspecialchars($product['url']) ?>" target="_blank" class="url-link">original</a>
                            <?php endif; ?>
                            <div style="margin-top: 10px;">
                            <span style="font-size: 11px;">📌</span>
                            <span style="margin-left: 3px;">Status overridden to '<?= htmlspecialchars($product['status_override']) ?>'</span>
                            <br>
                            <a href="javascript:void(0);" onclick="removeStatusOverride('<?= htmlspecialchars($product['id']) ?>')">remove status override</a>
                        <?php elseif ($effectiveStatus === 'down'): ?>
                            <?php if ($product['status_override']): ?>
                                <?php if ($product['external_url']): ?>
                                    <a href="<?= htmlspecialchars($product['external_url']) ?>" target="_blank" class="url-link"><?= htmlspecialchars($product['external_url']) ?></a>
                                <?php else: ?>
                                    <a href="<?= htmlspecialchars($product['url']) ?>" target="_blank" class="url-link">original</a>
                                <?php endif; ?>
                                <div style="margin-top: 10px;">
                                <span style="font-size: 11px;">📌</span>
                                <span style="margin-left: 3px;">Status overridden to '<?= htmlspecialchars($product['status_override']) ?>'</span>
                                <br>
                                <a href="javascript:void(0);" onclick="removeStatusOverride('<?= htmlspecialchars($product['id']) ?>')">remove status override</a>
                            <?php else: ?>
                                <span style="font-size: 11px;">❌ Empty</span>
                                <br>
                                <a href="javascript:void(0);" onclick="markUrlStatus('<?= htmlspecialchars($product['id']) ?>', 'up')">mark as up</a>
                            <?php endif; ?>
                            <a href="javascript:void(0);" onclick="openManualUrlPopup('<?= htmlspecialchars($product['id']) ?>')" style="margin-left: 5px;">add manual URL</a>
                        <?php else: ?>
                            <span style="font-size: 11px;">🕓</span>
                            <a href="<?= htmlspecialchars($product['url']) ?>" target="_blank" class="url-link">original</a>
                            <a href="javascript:void(0);" onclick="openManualUrlPopup('<?= htmlspecialchars($product['id']) ?>')" style="margin-left: 5px;">add manual URL</a>
                        <?php endif; ?>

                        <?php if (!$product['product_url'] && !$product['external_url'] && $product['external_url_status'] !== 'down'): ?>
                            <br>-
                        <?php endif; ?>
                    </td>
                    <td>
                        <?= $product['created_at'] ? date('M j, Y H:i', strtotime($product['created_at'])) : '-' ?>
                    </td>
                    <td>
                        <?= $product['updated_at'] ? date('M j, Y H:i', strtotime($product['updated_at'])) : '-' ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Manual URL Popup -->
    <div id="manualUrlPopup" class="popup-overlay">
        <div class="popup-content">
            <div class="popup-header">
                <h3>Add manual URL</h3>
                <span class="popup-close" onclick="closeManualUrlPopup()">&times;</span>
            </div>
            <form id="manualUrlForm" class="popup-form">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>">
                <input type="hidden" name="product_id" id="popup_product_id">
                <input type="hidden" name="action" value="save">

                <label for="manual_url">Website URL:</label>
                <input type="url" name="manual_url" id="manual_url" placeholder="https://example.com" required>

                <div class="popup-buttons">
                    <button type="submit">Save</button>
                    <button type="button" onclick="closeManualUrlPopup()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Temporary Notice Container -->
    <div id="tempNotice" class="temp-notice"></div>

    <!-- jQuery and DataTables JS -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    
    <script>
        // Available dates from PHP
        var availableDates = <?= json_encode($availableDates) ?>;
        var currentDateFilter = null;

        $(document).ready(function() {
            var table = $('#productsTable').DataTable({
                "pageLength": parseInt(getUrlParam('length')) || 10,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "order": [[5, "desc"], [3, "desc"]], // Sort by Launch Date (column 5) desc, then Votes (column 3) desc
                "displayStart": 0, // Always start at page 1, we'll restore pagination after filter is applied
                "search": {
                    "search": getUrlParam('search') || ''
                },
                "dom": 't<"bottom"p>', // Only show table and pagination, hide default controls
                "stateSave": false, // We'll handle state manually
                "processing": false, // Disable processing indicator for faster loading
                "deferRender": true, // Only render visible rows for better performance
                "columnDefs": [
                    { "orderable": false, "targets": [7] }, // Disable sorting on Links column
                    { "width": "150px", "targets": [0], "className": "text-truncate" }, // Product name
                    { "width": "120px", "targets": [1], "className": "text-truncate" }, // Tagline
                    { "width": "200px", "targets": [2] }, // Description
                    { "width": "60px", "targets": [3], "className": "text-center" }, // Votes
                    { "width": "60px", "targets": [4], "className": "text-center" }, // Rating
                    { "width": "90px", "targets": [5], "className": "text-center" }, // Launch Date
                    { "width": "120px", "targets": [6], "className": "text-truncate" }, // Categories
                    { "width": "180px", "targets": [7] }, // Links
                    { "width": "90px", "targets": [8], "className": "text-center" }, // Created
                    { "width": "90px", "targets": [9], "className": "text-center" }  // Updated
                ],
                "autoWidth": false,
                "fixedColumns": true,
                "scrollX": true, // Enable horizontal scrolling
                "autoWidth": false, // Let content determine width
                "language": {
                    "search": "Search products:",
                    "lengthMenu": "Show _MENU_ products per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ products",
                    "infoEmpty": "No products found",
                    "infoFiltered": "(filtered from _MAX_ total products)"
                }
            });

            // Restore state from URL parameters on page load
            function restoreStateFromUrl() {
                isRestoringState = true; // Prevent URL updates during restoration

                // Restore filter state first (this may reset pagination)
                var filterParam = getUrlParam('filter');
                if (filterParam === 'empty') {
                    // Apply search and update UI
                    table.column(7).search('❌').draw();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterEmpty').addClass('active').css('font-weight', 'bold');
                } else if (filterParam === 'manual') {
                    // Apply search for both URL overrides (🔧) and status overrides (📌) and update UI
                    table.column(7).search('🔧|📌', true, false).draw();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterManual').addClass('active').css('font-weight', 'bold');
                } else if (filterParam === 'unprocessed') {
                    // Apply search and update UI
                    table.column(7).search('🕓').draw();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterUnprocessed').addClass('active').css('font-weight', 'bold');
                } else if (filterParam === 'down') {
                    // Apply search and update UI
                    table.column(7).search('⚠️').draw();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterDown').addClass('active').css('font-weight', 'bold');
                } else {
                    // Clear search and update UI
                    table.column(7).search('').draw();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterAll').addClass('active').css('font-weight', 'bold');
                }

                // Restore date filter
                var dateParam = getUrlParam('date');
                if (dateParam && availableDates.includes(dateParam)) {
                    $('#datePicker').val(dateParam).trigger('change');
                }

                // Restore pagination AFTER filter is applied
                var startParam = parseInt(getUrlParam('start')) || 0;
                if (startParam > 0) {
                    var pageLength = table.page.len();
                    var targetPage = Math.floor(startParam / pageLength);
                    table.page(targetPage).draw(false); // false = don't reset paging
                }

                // Re-enable URL updates after restoration is complete
                setTimeout(function() {
                    isRestoringState = false;
                }, 100); // Balanced timeout for proper state restoration
            }

            // Set up custom controls
            setupCustomControls();

            // Call restore function after table is fully initialized
            setTimeout(function() {
                restoreStateFromUrl();
                handleNotificationFromUrl();
                updateResultsInfo(); // Update results info after table is ready
            }, 300); // Quick delay for proper filter then pagination restoration

            // Handle notification from URL parameters
            function handleNotificationFromUrl() {
                var notificationParam = getUrlParam('notification');
                var notificationTypeParam = getUrlParam('notification_type') || 'success';

                if (notificationParam) {
                    // Show the notification
                    showTempNotice(decodeURIComponent(notificationParam), notificationTypeParam);

                    // Remove notification parameters from URL after showing
                    setTimeout(function() {
                        removeNotificationFromUrl();
                    }, 4500); // Remove after notification disappears
                }
            }

            // Remove notification parameters from URL
            function removeNotificationFromUrl() {
                var url = new URL(window.location);
                url.searchParams.delete('notification');
                url.searchParams.delete('notification_type');

                // Update URL without page refresh
                window.history.replaceState({}, document.title, url.toString());
            }

            // Add event listeners to preserve URL state
            table.on('page.dt', function() {
                var info = table.page.info();
                updateUrlParams({
                    'start': info.start,
                    'length': info.length
                });
                updateResultsInfo();
            });

            table.on('length.dt', function(e, settings, len) {
                updateUrlParams({
                    'length': len,
                    'start': 0 // Reset to first page when changing page length
                });
                updateResultsInfo();
            });

            table.on('draw.dt', function() {
                updateResultsInfo();
            });

            // Setup custom controls
            function setupCustomControls() {
                // Set initial values from URL params
                var lengthParam = getUrlParam('length') || '10';
                var searchParam = getUrlParam('search') || '';

                $('#productsPerPage').val(lengthParam);
                $('#searchBox').val(searchParam);

                // Handle page length change
                $('#productsPerPage').on('change', function() {
                    var newLength = parseInt($(this).val());
                    table.page.len(newLength).draw();
                });

                // Handle search
                $('#searchBox').on('keyup search', function() {
                    var searchValue = $(this).val();
                    table.search(searchValue).draw();
                    updateUrlParams({ 'search': searchValue || null, 'start': 0 });
                });
            }

            // Update results info
            function updateResultsInfo() {
                var info = table.page.info();
                var resultsText = '';

                if (info.recordsDisplay === 0) {
                    resultsText = 'No products found';
                } else if (info.recordsTotal === info.recordsDisplay) {
                    resultsText = 'Showing ' + (info.start + 1) + ' to ' + info.end + ' of ' + info.recordsDisplay + ' products';
                } else {
                    resultsText = 'Showing ' + (info.start + 1) + ' to ' + info.end + ' of ' + info.recordsDisplay + ' products (filtered from ' + info.recordsTotal + ' total products)';
                }

                $('#resultsInfo').text(resultsText);
            }

            table.on('search.dt', function() {
                var searchValue = table.search();
                updateUrlParams({
                    'search': searchValue,
                    'start': 0 // Reset to first page when searching
                });
            });

            // Update live time every second (both local and Pacific time)
            function updateLiveTime() {
                const now = new Date();

                // Local time
                const localTime = now.toLocaleString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                // Pacific time (Product Hunt timezone)
                const pacificTime = now.toLocaleString('en-US', {
                    timeZone: 'America/Los_Angeles',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                document.getElementById('localTime').textContent = 'Local: ' + localTime;
                document.getElementById('pacificTime').textContent = 'Pacific: ' + pacificTime;
            }

            // Update immediately and then every second
            updateLiveTime();
            setInterval(updateLiveTime, 1000);

            // Fix DataTables header on window resize
            $(window).on('resize', function() {
                $('#productsTable').DataTable().columns.adjust().draw();
            });

            // Filter functionality
            $('#filterAll').click(function(e) {
                e.preventDefault();
                table.column(7).search('').draw(); // Clear search on Links column
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': null });
            });

            $('#filterEmpty').click(function(e) {
                e.preventDefault();
                table.column(7).search('❌').draw(); // Search for ❌ in Links column
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'empty' });
            });

            $('#filterUnprocessed').click(function(e) {
                e.preventDefault();
                table.column(7).search('🕓').draw(); // Search for 🕓 in Links column
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'unprocessed' });
            });

            $('#filterManual').click(function(e) {
                e.preventDefault();
                // Search for both URL overrides (🔧) and status overrides (📌) in Links column
                table.column(7).search('🔧|📌', true, false).draw(); // Use regex to search for either symbol
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'manual' });
            });

            $('#filterDown').click(function(e) {
                e.preventDefault();
                table.column(7).search('⚠️').draw(); // Search for ⚠️ in Links column
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'down' });
            });

            // Set up date picker with available dates
            var datePicker = document.getElementById('datePicker');
            datePicker.min = availableDates[availableDates.length - 1]; // Oldest date
            datePicker.max = availableDates[0]; // Newest date

            // Date filter functionality
            $('#datePicker').change(function() {
                var selectedDate = $(this).val();

                if (selectedDate === '') {
                    // Clear date filter
                    currentDateFilter = null;
                    table.column(5).search('').draw();
                    updateUrlParams({ 'date': null });
                } else if (availableDates.includes(selectedDate)) {
                    currentDateFilter = selectedDate;

                    // Apply date filter to Launch Date column (column 5)
                    var formattedDate = new Date(selectedDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });

                    table.column(5).search(formattedDate).draw();
                    updateUrlParams({ 'date': selectedDate });
                } else {
                    // Invalid date selected, clear it
                    $(this).val('');
                    currentDateFilter = null;
                    table.column(5).search('').draw();
                    updateUrlParams({ 'date': null, 'start': 0 });
                    alert('Please select a valid date with available data.');
                }
            });

            // Reset date filter
            $('#resetDateFilter').click(function(e) {
                e.preventDefault();
                $('#datePicker').val('');
                currentDateFilter = null;
                table.column(5).search('').draw();
                updateUrlParams({ 'date': null, 'start': 0 });
            });

            // Keep date filter active when other filters are applied
            $('#filterAll, #filterEmpty, #filterManual, #filterUnprocessed, #filterDown').click(function() {
                if (currentDateFilter) {
                    var formattedDate = new Date(currentDateFilter).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });

                    // Reapply date filter after status filter
                    setTimeout(function() {
                        table.column(5).search(formattedDate).draw();
                    }, 10);
                }
            });
        });

        // Temporary notice functions
        function showTempNotice(message, type = 'success', duration = 4000) {
            const notice = document.getElementById('tempNotice');
            notice.textContent = message;
            notice.className = 'temp-notice ' + type;

            // Show notice
            setTimeout(() => notice.classList.add('show'), 100);

            // Hide notice after duration
            setTimeout(() => {
                notice.classList.remove('show');
                setTimeout(() => {
                    notice.textContent = '';
                    notice.className = 'temp-notice';
                }, 300);
            }, duration);
        }

        // URL state management functions
        var isRestoringState = false; // Flag to prevent URL updates during state restoration

        function updateUrlParams(params) {
            if (isRestoringState) return; // Don't update URL during state restoration

            const url = new URL(window.location);
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== '') {
                    url.searchParams.set(key, params[key]);
                } else {
                    url.searchParams.delete(key);
                }
            });
            window.history.replaceState({}, '', url);
        }

        function getUrlParam(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        function getCurrentUrlWithParams() {
            return window.location.href;
        }

        // Manual URL popup functions
        function openManualUrlPopup(productId) {
            document.getElementById('popup_product_id').value = productId;
            document.getElementById('manual_url').value = '';
            document.getElementById('manualUrlPopup').style.display = 'block';
            document.getElementById('manual_url').focus();
        }

        function closeManualUrlPopup() {
            document.getElementById('manualUrlPopup').style.display = 'none';
        }

        // Mark URL status function
        function markUrlStatus(productId, status) {
            if (!confirm(`Are you sure you want to mark this URL as ${status}?`)) {
                return;
            }

            fetch('api/mark_url_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: parseInt(productId),
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect with success notification parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent(`URL status updated to '${status}' successfully!`) + '&notification_type=success';
                } else {
                    // Show error notification via URL parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Error: ' + (data.error || 'Unknown error occurred')) + '&notification_type=error';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Show error notification via URL parameter
                var currentUrl = getCurrentUrlWithParams();
                var separator = currentUrl.includes('?') ? '&' : '?';
                window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Network error occurred. Please try again.') + '&notification_type=error';
            });
        }

        // Close popup on Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeManualUrlPopup();
            }
        });

        // Handle manual URL form submission
        document.getElementById('manualUrlForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('type', 'url'); // Add type parameter for URL overrides

            fetch('api/manual_url_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeManualUrlPopup();
                    // Redirect with success notification parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Manual URL saved successfully!') + '&notification_type=success';
                } else {
                    // Show error notification via URL parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Error: ' + data.message) + '&notification_type=error';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTempNotice('An error occurred while saving the URL.', 'error');
            });
        });

        // Remove manual URL override
        function removeManualUrl(productId) {
            if (!confirm('Are you sure you want to remove the manual URL override?')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'remove');
            formData.append('product_id', productId);
            formData.append('type', 'url');
            formData.append('csrf_token', '<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>');

            fetch('api/manual_url_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect with success notification parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Manual URL override removed successfully!') + '&notification_type=success';
                } else {
                    // Show error notification via URL parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Error: ' + (data.message || 'Unknown error occurred')) + '&notification_type=error';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Show error notification via URL parameter
                var currentUrl = getCurrentUrlWithParams();
                var separator = currentUrl.includes('?') ? '&' : '?';
                window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('An error occurred while removing the override.') + '&notification_type=error';
            });
        }

        // Remove status override
        function removeStatusOverride(productId) {
            if (!confirm('Are you sure you want to remove the status override?')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'remove');
            formData.append('product_id', productId);
            formData.append('type', 'status');
            formData.append('csrf_token', '<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>');

            fetch('api/manual_url_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Redirect with success notification parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Status override removed successfully!') + '&notification_type=success';
                } else {
                    // Show error notification via URL parameter
                    var currentUrl = getCurrentUrlWithParams();
                    var separator = currentUrl.includes('?') ? '&' : '?';
                    window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Error: ' + (data.message || 'Unknown error occurred')) + '&notification_type=error';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Show error notification via URL parameter
                var currentUrl = getCurrentUrlWithParams();
                var separator = currentUrl.includes('?') ? '&' : '?';
                window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('An error occurred while removing the status override.') + '&notification_type=error';
            });
        }

    </script>
</body>
</html>
