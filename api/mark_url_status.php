<?php
/**
 * API endpoint to mark URL status as up or down
 */

require_once __DIR__ . '/../includes/auth.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database_setup.php';

// Check authentication
Auth::requireAuth();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['product_id']) || !isset($input['status'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

$productId = (int)$input['product_id'];
$status = $input['status'];

// Validate status
if (!in_array($status, ['up', 'down'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid status. Must be "up" or "down"']);
    exit;
}

try {
    // Connect to database
    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    // Create or update status override instead of updating the products table
    $stmt = $pdo->prepare("
        INSERT INTO overrides (product_id, type, rule)
        VALUES (?, 'status', ?)
        ON DUPLICATE KEY UPDATE
            rule = VALUES(rule),
            updated_at = CURRENT_TIMESTAMP
    ");
    $result = $stmt->execute([$productId, $status]);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => "Status override created: '$status'",
            'product_id' => $productId,
            'new_status' => $status
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to create status override']);
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Server error: ' . $e->getMessage()]);
}
?>
