<?php
/**
 * Manual URL Override API
 * Handles saving and removing manual URL overrides
 */

// Start output buffering to prevent any accidental output
ob_start();

// Suppress any error output that might interfere with JSON
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 0);

require_once '../config/config.php';
require_once '../includes/auth.php';

// Require authentication
Auth::requireAuth();

// Clean any output that might have been generated multiple times
ob_clean();
ob_start(); // Start fresh buffer

// Set JSON response header
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Verify CSRF token
$csrfToken = $_POST['csrf_token'] ?? '';
if (!Auth::verifyCSRFToken($csrfToken)) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

// Get action and parameters
$action = $_POST['action'] ?? '';
$productId = $_POST['product_id'] ?? '';

if (empty($productId)) {
    http_response_code(400);
    ob_clean(); // Clean buffer before JSON output
    echo json_encode(['success' => false, 'message' => 'Product ID is required']);
    exit;
}

try {
    // Connect to database
    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    if ($action === 'save') {
        $manualUrl = $_POST['manual_url'] ?? '';
        
        if (empty($manualUrl)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'URL is required']);
            exit;
        }
        
        // Validate URL format
        if (!filter_var($manualUrl, FILTER_VALIDATE_URL)) {
            http_response_code(400);
            ob_clean(); // Clean buffer before JSON output
            echo json_encode(['success' => false, 'message' => 'Invalid URL format']);
            exit;
        }

        // Strip URL parameters (query string and fragment)
        $parsedUrl = parse_url($manualUrl);
        $cleanUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        if (isset($parsedUrl['port'])) {
            $cleanUrl .= ':' . $parsedUrl['port'];
        }
        if (isset($parsedUrl['path'])) {
            $cleanUrl .= $parsedUrl['path'];
        }
        // Intentionally omit query and fragment
        $manualUrl = $cleanUrl;
        
        // Check if product exists
        $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        if (!$stmt->fetch()) {
            http_response_code(404);
            ob_clean(); // Clean buffer before JSON output
            echo json_encode(['success' => false, 'message' => 'Product not found']);
            exit;
        }
        
        // Get the type parameter (default to 'url' for backward compatibility)
        $type = $_POST['type'] ?? 'url';

        // Save or update override
        $stmt = $pdo->prepare("
            INSERT INTO overrides (product_id, type, rule)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
                rule = VALUES(rule),
                updated_at = CURRENT_TIMESTAMP
        ");
        $stmt->execute([$productId, $type, $manualUrl]);
        
        ob_clean(); // Clean buffer before JSON output
        echo json_encode([
            'success' => true,
            'message' => 'Manual URL saved successfully',
            'url' => $manualUrl
        ]);
        
    } elseif ($action === 'remove') {
        // Get the type parameter (default to 'url' for backward compatibility)
        $type = $_POST['type'] ?? 'url';

        // Remove override
        $stmt = $pdo->prepare("DELETE FROM overrides WHERE product_id = ? AND type = ?");
        $stmt->execute([$productId, $type]);

        if ($stmt->rowCount() > 0) {
            ob_clean(); // Clean buffer before JSON output
            echo json_encode([
                'success' => true,
                'message' => ucfirst($type) . ' override removed successfully'
            ]);
        } else {
            ob_clean(); // Clean buffer before JSON output
            echo json_encode([
                'success' => false,
                'message' => 'No ' . $type . ' override found for this product'
            ]);
        }
        
    } else {
        http_response_code(400);
        ob_clean(); // Clean buffer before JSON output
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    // Clean any output buffer before sending JSON
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

// End output buffering and send clean response
ob_end_flush();
?>
