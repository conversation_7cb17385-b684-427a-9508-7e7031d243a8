<?php
/**
 * Product Hunt API Fetcher
 * Fetches today's launched products from Product Hunt GraphQL API
 */

require_once __DIR__ . '/../config/config.php';
require_once 'database_setup.php';
require_once 'url_utils.php';

class ProductHuntAPI {
    private $apiUrl = 'https://api.producthunt.com/v2/api/graphql';
    private $apiKey;
    private $pdo;
    private $categoryManager;
    private $rateLimitInfo = [];
    private $batchSize = 20; // Products per API request
    private $targetDate = null;
    private $startDate = null;
    private $endDate = null;
    private $maxPages = null;

    public function __construct($options = []) {
        validateConfig();
        $this->apiKey = PRODUCT_HUNT_API_KEY;
        $this->connectToDatabase();
        $this->categoryManager = new CategoryManager($this->pdo);

        // Set date filters
        if (isset($options['date'])) {
            $this->targetDate = $options['date'];
        } elseif (isset($options['start_date']) && isset($options['end_date'])) {
            $this->startDate = $options['start_date'];
            $this->endDate = $options['end_date'];
        } else {
            // Default to today
            $this->targetDate = date('Y-m-d');
        }

        // Set max pages limit
        if (isset($options['max_pages']) && $options['max_pages'] > 0) {
            $this->maxPages = $options['max_pages'];
            echo "🔢 Limiting fetch to {$this->maxPages} pages\n";
        }
    }
    
    private function connectToDatabase() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $this->pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]);
        } catch (PDOException $e) {
            die("❌ Database connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    public function fetchAllTodaysPosts() {
        $allProducts = [];
        $hasNextPage = true;
        $cursor = null;
        $pageCount = 0;

        while ($hasNextPage) {
            $pageCount++;
            echo "📄 Fetching page $pageCount...\n";

            // Check if we've reached the max pages limit
            if ($this->maxPages && $pageCount > $this->maxPages) {
                echo "🔢 Reached max pages limit ({$this->maxPages}), stopping fetch\n";
                break;
            }

            try {
                $pageData = $this->fetchPostsPage($cursor);

                if (empty($pageData['edges'])) {
                    break;
                }

                $allProducts = array_merge($allProducts, $pageData['edges']);
                $hasNextPage = $pageData['pageInfo']['hasNextPage'] ?? false;
                $cursor = $pageData['pageInfo']['endCursor'] ?? null;

                $this->displayRateLimitInfo();

                // Small delay to be respectful to the API
                if ($hasNextPage) {
                    sleep(1);
                }

            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Rate limit reached') !== false) {
                    echo "⚠️  " . $e->getMessage() . "\n";
                    echo "📊 Successfully fetched " . count($allProducts) . " products before hitting rate limit.\n";
                    break;
                } else {
                    throw $e; // Re-throw other exceptions
                }
            }
        }

        echo "✅ Fetched " . count($allProducts) . " products across $pageCount pages\n\n";
        return $allProducts;
    }

    private function fetchPostsPage($cursor = null) {
        $variables = [
            'first' => $this->batchSize
        ];

        if ($cursor) {
            $variables['after'] = $cursor;
        }

        // Add date filtering (Product Hunt uses Pacific Time)
        if ($this->targetDate) {
            // Convert to Pacific Time properly
            $startTime = new DateTime($this->targetDate . ' 00:00:00', new DateTimeZone('America/Los_Angeles'));
            $endTime = new DateTime($this->targetDate . ' 23:59:59', new DateTimeZone('America/Los_Angeles'));

            $variables['postedAfter'] = $startTime->format('c'); // ISO 8601 format
            $variables['postedBefore'] = $endTime->format('c');

            echo "🔍 Filtering for date {$this->targetDate} (Pacific Time)\n";
        } elseif ($this->startDate && $this->endDate) {
            $startTime = new DateTime($this->startDate . ' 00:00:00', new DateTimeZone('America/Los_Angeles'));
            $endTime = new DateTime($this->endDate . ' 23:59:59', new DateTimeZone('America/Los_Angeles'));

            $variables['postedAfter'] = $startTime->format('c');
            $variables['postedBefore'] = $endTime->format('c');

            echo "🔍 Filtering for date range {$this->startDate} to {$this->endDate} (Pacific Time)\n";
        }

        $query = [
            'query' => '
                query fetchPosts($first: Int, $after: String, $postedAfter: DateTime, $postedBefore: DateTime) {
                    posts(first: $first, after: $after, postedAfter: $postedAfter, postedBefore: $postedBefore) {
                        edges {
                            node {
                                id
                                name
                                tagline
                                description
                                votesCount
                                website
                                url
                                createdAt
                                reviewsRating
                                reviewsCount
                                topics {
                                    edges {
                                        node {
                                            name
                                        }
                                    }
                                }
                            }
                        }
                        pageInfo {
                            hasNextPage
                            endCursor
                        }
                    }
                }
            ',
            'variables' => $variables
        ];
        
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
            'Host: api.producthunt.com'
        ];

        $responseHeaders = [];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($query),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_HEADERFUNCTION => function($curl, $header) use (&$responseHeaders) {
                // Suppress unused parameter warning
                unset($curl);
                $len = strlen($header);
                $header = explode(':', $header, 2);
                if (count($header) < 2) return $len;

                $name = strtolower(trim($header[0]));
                $value = trim($header[1]);
                $responseHeaders[$name] = $value;

                return $len;
            }
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // Store rate limit information - try different header formats
        $this->rateLimitInfo = [
            'limit' => $responseHeaders['x-ratelimit-limit'] ??
                      $responseHeaders['x-rate-limit-limit'] ??
                      $responseHeaders['ratelimit-limit'] ?? 'Unknown',
            'remaining' => $responseHeaders['x-ratelimit-remaining'] ??
                          $responseHeaders['x-rate-limit-remaining'] ??
                          $responseHeaders['ratelimit-remaining'] ?? 'Unknown',
            'reset' => $responseHeaders['x-ratelimit-reset'] ??
                      $responseHeaders['x-rate-limit-reset'] ??
                      $responseHeaders['ratelimit-reset'] ?? 'Unknown'
        ];

        // Also check if rate limit info is in the response body
        if (isset($data['errors'])) {
            foreach ($data['errors'] as $error) {
                if (isset($error['details']['limit'])) {
                    $this->rateLimitInfo['limit'] = $error['details']['limit'];
                    $this->rateLimitInfo['remaining'] = $error['details']['remaining'];
                    $this->rateLimitInfo['reset'] = time() + ($error['details']['reset_in'] ?? 0);
                }
            }
        }

        if (curl_error($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error: $error");
        }

        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("API request failed with HTTP code: $httpCode. Response: $response");
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON decode error: " . json_last_error_msg());
        }
        
        if (isset($data['errors'])) {
            // Check if it's a rate limit error
            foreach ($data['errors'] as $error) {
                if (isset($error['error']) && $error['error'] === 'rate_limit_reached') {
                    $resetIn = $error['details']['reset_in'] ?? 'unknown';
                    throw new Exception("Rate limit reached. Reset in {$resetIn} seconds. Please wait and try again.");
                }
            }
            throw new Exception("GraphQL errors: " . json_encode($data['errors']));
        }

        return $data['data']['posts'] ?? [];
    }

    private function displayRateLimitInfo() {
        if (!empty($this->rateLimitInfo)) {
            echo "🚦 Rate limit: {$this->rateLimitInfo['remaining']}/{$this->rateLimitInfo['limit']} remaining";
            if ($this->rateLimitInfo['reset'] !== 'Unknown') {
                $resetTime = date('H:i:s', $this->rateLimitInfo['reset']);
                echo " (resets at $resetTime)";
            }
            echo "\n";
        }
    }
    
    public function saveProductsToDatabase($products) {
        $insertedCount = 0;
        $updatedCount = 0;
        $errorCount = 0;

        foreach ($products as $edge) {
            $product = $edge['node'];

            try {
                // Check if product already exists
                $exists = $this->productExists($product['id']);

                if ($exists) {
                    // Update existing product
                    $this->updateProduct($product);
                    echo "🔄 Updated product: {$product['name']}\n";
                    $updatedCount++;
                } else {
                    // Insert new product
                    $this->insertProduct($product);
                    echo "✅ Inserted product: {$product['name']}\n";
                    $insertedCount++;
                }

                // Handle categories/topics (always update these)
                if (isset($product['topics']['edges']) && !empty($product['topics']['edges'])) {
                    // Clear existing categories for this product
                    $this->clearProductCategories($product['id']);
                    // Assign new categories
                    $this->assignCategoriesToProduct($product['id'], $product['topics']['edges']);
                }

                // URL resolution is now handled by separate resolve_urls.php script

            } catch (Exception $e) {
                echo "❌ Error processing product '{$product['name']}': " . $e->getMessage() . "\n";
                $errorCount++;
            }
        }

        return [
            'inserted' => $insertedCount,
            'updated' => $updatedCount,
            'errors' => $errorCount
        ];
    }
    
    private function productExists($productId) {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function insertProduct($product) {
        $sql = "INSERT INTO products (
            id, name, tagline, description, votes_count, review_rating,
            review_count, product_url, date_created, url
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->pdo->prepare($sql);

        // Parse the created date
        $dateCreated = null;
        if (isset($product['createdAt'])) {
            $dateCreated = date('Y-m-d', strtotime($product['createdAt']));
        }

        // Clean both URLs by removing parameters
        $websiteUrl = $this->cleanWebsiteUrl($product['website'] ?? '');
        $productUrl = $this->cleanWebsiteUrl($product['url'] ?? '');

        $stmt->execute([
            $product['id'],
            $product['name'] ?? '',
            $product['tagline'] ?? '',
            $product['description'] ?? '',
            $product['votesCount'] ?? 0,
            $product['reviewsRating'] ?? null,
            $product['reviewsCount'] ?? null,
            $productUrl,
            $dateCreated,
            $websiteUrl
        ]);
    }

    private function updateProduct($product) {
        $sql = "UPDATE products SET
            name = ?, tagline = ?, description = ?, votes_count = ?, review_rating = ?,
            review_count = ?, product_url = ?, date_created = ?, url = ?,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";

        $stmt = $this->pdo->prepare($sql);

        // Parse the created date
        $dateCreated = null;
        if (isset($product['createdAt'])) {
            $dateCreated = date('Y-m-d', strtotime($product['createdAt']));
        }

        // Clean both URLs by removing parameters
        $websiteUrl = $this->cleanWebsiteUrl($product['website'] ?? '');
        $productUrl = $this->cleanWebsiteUrl($product['url'] ?? '');

        $stmt->execute([
            $product['name'] ?? '',
            $product['tagline'] ?? '',
            $product['description'] ?? '',
            $product['votesCount'] ?? 0,
            $product['reviewsRating'] ?? null,
            $product['reviewsCount'] ?? null,
            $productUrl,
            $dateCreated,
            $websiteUrl,
            $product['id']
        ]);
    }



    private function clearProductCategories($productId) {
        $stmt = $this->pdo->prepare("DELETE FROM product_categories WHERE product_id = ?");
        $stmt->execute([$productId]);
    }

    private function cleanWebsiteUrl($url) {
        if (empty($url)) {
            return '';
        }

        // Parse the URL
        $parsedUrl = parse_url($url);

        if (!$parsedUrl) {
            return $url; // Return original if parsing fails
        }

        // Rebuild URL without query parameters and fragments
        $cleanUrl = '';

        if (isset($parsedUrl['scheme'])) {
            $cleanUrl .= $parsedUrl['scheme'] . '://';
        }

        if (isset($parsedUrl['user'])) {
            $cleanUrl .= $parsedUrl['user'];
            if (isset($parsedUrl['pass'])) {
                $cleanUrl .= ':' . $parsedUrl['pass'];
            }
            $cleanUrl .= '@';
        }

        if (isset($parsedUrl['host'])) {
            $cleanUrl .= $parsedUrl['host'];
        }

        if (isset($parsedUrl['port'])) {
            $cleanUrl .= ':' . $parsedUrl['port'];
        }

        if (isset($parsedUrl['path'])) {
            $cleanUrl .= $parsedUrl['path'];
        }

        return $cleanUrl;
    }


    
    private function assignCategoriesToProduct($productId, $topics) {
        foreach ($topics as $topicEdge) {
            $topic = $topicEdge['node'];
            $categoryName = $topic['name'] ?? '';

            if (empty($categoryName)) {
                continue;
            }

            // Add category if it doesn't exist
            $categoryId = $this->categoryManager->getCategoryId($categoryName);
            if (!$categoryId) {
                $categoryId = $this->categoryManager->addCategory($categoryName);
            }

            if ($categoryId) {
                // Link product to category
                $stmt = $this->pdo->prepare("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)");
                $stmt->execute([$productId, $categoryId]);
            }
        }
    }



    public function displayResults($results) {
        echo "\n📊 Processing Results:\n";
        echo "- Products inserted: {$results['inserted']}\n";
        echo "- Products updated: {$results['updated']}\n";
        echo "- Errors: {$results['errors']}\n";
        echo "- Total processed: " . ($results['inserted'] + $results['updated'] + $results['errors']) . "\n";
    }




}

// Helper function to parse command line arguments
function parseArguments($argv) {
    $options = [];

    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];

        if (strpos($arg, '--date=') === 0) {
            $options['date'] = substr($arg, 7);
        } elseif (strpos($arg, '--start-date=') === 0) {
            $options['start_date'] = substr($arg, 13);
        } elseif (strpos($arg, '--end-date=') === 0) {
            $options['end_date'] = substr($arg, 11);
        } elseif (strpos($arg, '--max-pages=') === 0) {
            $options['max_pages'] = (int)substr($arg, 12);
        } elseif ($arg === '--help' || $arg === '-h') {
            echo "Usage: php fetch_products.php [OPTIONS]\n\n";
            echo "Options:\n";
            echo "  --date=YYYY-MM-DD          Fetch products from specific date\n";
            echo "  --start-date=YYYY-MM-DD    Fetch products from date range (requires --end-date)\n";
            echo "  --end-date=YYYY-MM-DD      Fetch products to date range (requires --start-date)\n";
            echo "  --max-pages=N              Limit fetching to N pages (for testing)\n";
            echo "  --help, -h                 Show this help message\n\n";
            echo "Examples:\n";
            echo "  php fetch_products.php                    # Fetch today's products\n";
            echo "  php fetch_products.php --date=2024-01-15  # Fetch products from Jan 15, 2024\n";
            echo "  php fetch_products.php --start-date=2024-01-01 --end-date=2024-01-31  # Fetch January 2024\n";
            exit(0);
        }
    }

    return $options;
}

// Run the fetcher if this file is executed directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    $startTime = microtime(true);
    $startDateTime = date('Y-m-d H:i:s');

    $options = parseArguments($argv ?? []);

    echo "🚀 Starting Product Hunt data fetch...\n";
    echo "⏰ Start time: $startDateTime\n";

    // Display date filter info
    if (isset($options['date'])) {
        echo "📅 Fetching products from: {$options['date']}\n\n";
    } elseif (isset($options['start_date']) && isset($options['end_date'])) {
        echo "📅 Fetching products from: {$options['start_date']} to {$options['end_date']}\n\n";
    } else {
        echo "📅 Fetching today's products (" . date('Y-m-d') . ")\n\n";
    }

    try {
        $fetcher = new ProductHuntAPI($options);

        // Normal processing with API calls
        echo "📡 Fetching products from Product Hunt API...\n";
        $products = $fetcher->fetchAllTodaysPosts();

        if (empty($products)) {
            echo "ℹ️  No products found for the specified date(s).\n";
            exit(0);
        }

        echo "📋 Processing " . count($products) . " products...\n\n";

        echo "💾 Saving products to database...\n";
        $results = $fetcher->saveProductsToDatabase($products);

        $fetcher->displayResults($results);



        $endTime = microtime(true);
        $endDateTime = date('Y-m-d H:i:s');
        $duration = $endTime - $startTime;
        $durationFormatted = gmdate('H:i:s', (int)$duration);

        echo "\n⏰ End time: $endDateTime\n";
        echo "⏱️  Total duration: $durationFormatted (" . number_format($duration, 2) . " seconds)\n";
        echo "\n✨ Product Hunt data fetch completed!\n";

    } catch (Exception $e) {
        $endTime = microtime(true);
        $endDateTime = date('Y-m-d H:i:s');
        $duration = $endTime - $startTime;
        $durationFormatted = gmdate('H:i:s', (int)$duration);

        echo "\n⏰ End time: $endDateTime\n";
        echo "⏱️  Total duration: $durationFormatted (" . number_format($duration, 2) . " seconds)\n";
        echo "❌ Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
